<script setup>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { nextTick, onBeforeUnmount, onMounted, ref, toRaw } from 'vue';
import ModelComponent from './ModelComponent.vue';

const props = defineProps({
  // 场景配置
  sceneConfig: {
    type: Object,
    default: () => ({
      background: 0xffffff,
      lights: [
        { type: 'ambient', color: 0xffffff, intensity: 2 },
        { type: 'directional', color: 0xffffff, intensity: 6, position: { x: 1, y: 2, z: 3 } },
      ],
      camera: {
        position: { x: 100, y: 200, z: 100 }, // 更高更远的位置以观察400x400地图
        lookAt: { x: 1000, y: 400, z: 100 },
      },
    }),
  },
  // 模型配置数组
  models: {
    type: Array,
    default: () => [],
  },
  // 是否启用轨道控制器
  enableControls: {
    type: Boolean,
    default: true,
  },
  // 容器高度
  height: {
    type: String,
    default: '500px',
  },
});

const emit = defineEmits(['modelLoaded', 'modelError', 'sceneReady', 'model-right-clicked']);

const threeContainer = ref(null);
const scene = ref(null);
const camera = ref(null);
const renderer = ref(null);
const controls = ref(null);
const clock = ref(null);
const modelRefs = ref({});
const loadedModels = ref({});
const animationId = ref(null);
const resizeObserver = ref(null);

// 天气粒子系统
const weatherParticles = ref(null);
const currentWeatherType = ref('clear');

// 鼠标交互
const raycaster = ref(new THREE.Raycaster());
const mouse = ref(new THREE.Vector2());

// 自由视角控制 - 修改为右键拖拽控制
const freeCameraMode = ref(false);
const cameraMovementSpeed = ref(20.0); // 相机移动速度，默认20

// 第一人称视角控制
const firstPersonMode = ref(false);

// 鼠标右键拖拽控制相关
const rightMouseDragState = ref({
  isRightMouseDown: false,
  lastMouseX: 0,
  lastMouseY: 0,
  sensitivity: 0.5, // 拖拽灵敏度
  isDragging: false,
});

// 相机拖拽移动相关
const cameraDragMovement = ref({
  isActive: false,
  startPosition: new THREE.Vector3(),
  currentPosition: new THREE.Vector3(),
  dragSensitivity: 0.01, // 拖拽移动灵敏度
});

// 初始化Three.js场景
const initThree = () => {
  // 创建场景
  scene.value = new THREE.Scene();
  scene.value.background = new THREE.Color(props.sceneConfig.background || 0xffffff);

  // 获取容器尺寸
  const container = threeContainer.value;
  const width = container.clientWidth;
  const height = container.clientHeight;

  // 创建相机（优化近远裁剪面以防止闪烁）
  camera.value = new THREE.PerspectiveCamera(75, width / height, 0.01, 10000);

  // 设置相机位置
  const cameraConfig = props.sceneConfig.camera || {};
  camera.value.position.set(
    cameraConfig.position?.x || 0,
    cameraConfig.position?.y || 1,
    cameraConfig.position?.z || 5,
  );

  // 设置相机朝向
  if (cameraConfig.lookAt) {
    camera.value.lookAt(cameraConfig.lookAt.x || 0, cameraConfig.lookAt.y || 0, cameraConfig.lookAt.z || 0);
  }

  // 创建渲染器
  renderer.value = new THREE.WebGLRenderer({
    antialias: true,
    powerPreference: 'high-performance',
  });
  renderer.value.setSize(width, height);
  renderer.value.setPixelRatio(window.devicePixelRatio);
  // 启用阴影
  renderer.value.shadowMap.enabled = true;
  //  renderer.value.shadowMap.type = THREE.PCFSoftShadowMap; // 使用软阴影
  renderer.value.shadowMap.type = THREE.VSMShadowMap; // 使用软阴影
  container.appendChild(renderer.value.domElement);

  // 添加轨道控制器
  if (props.enableControls) {
    controls.value = new OrbitControls(camera.value, renderer.value.domElement);
    controls.value.enableDamping = true;
    controls.value.dampingFactor = 0.05;

    // 配置鼠标控制
    controls.value.mouseButtons = {
      LEFT: THREE.MOUSE.ROTATE, // 左键旋转视角
      MIDDLE: THREE.MOUSE.DOLLY, // 中键缩放
      RIGHT: null, // 右键用于自由相机拖拽控制
    };

    // 启用键盘修饰键平移（Shift+左键拖拽平移）
    controls.value.keys = {
      LEFT: 'ArrowLeft', // 左箭头键
      UP: 'ArrowUp', // 上箭头键
      RIGHT: 'ArrowRight', // 右箭头键
      BOTTOM: 'ArrowDown', // 下箭头键
    };

    // 配置触摸控制（移动设备）
    controls.value.touches = {
      ONE: THREE.TOUCH.ROTATE, // 单指旋转
      TWO: THREE.TOUCH.DOLLY_PAN, // 双指缩放和平移
    };

    // 设置旋转限制
    controls.value.enableRotate = true;
    controls.value.rotateSpeed = 1.0;

    // 设置缩放限制（适配400x400地图）
    controls.value.enableZoom = true;
    controls.value.zoomSpeed = 1.0;
    controls.value.minDistance = 5;
    controls.value.maxDistance = 800; // 增大最大距离以适应400x400地图

    // 设置平移限制（启用平移以便探索400x400地图）
    controls.value.enablePan = true; // 启用平移，便于探索大地图
    controls.value.panSpeed = 1.0;

    // 设置垂直旋转限制
    controls.value.minPolarAngle = 0; // 可以看到正上方
    controls.value.maxPolarAngle = Math.PI; // 可以看到正下方

    // 如果配置了lookAt，设置控制器的target
    if (cameraConfig.lookAt) {
      controls.value.target.set(cameraConfig.lookAt.x || 0, cameraConfig.lookAt.y || 0, cameraConfig.lookAt.z || 0);
    }

    // 添加控制器变化监听，动态优化相机参数防止闪烁
    controls.value.addEventListener('change', optimizeCameraParameters);
    controls.value.addEventListener('end', optimizeCameraParameters);

    console.log('相机控制配置：鼠标左键旋转视角，中键缩放，箭头键平移（探索400x400地图）');
  }

  // 添加灯光
  addLights();

  // 添加地面
  addGround();

  // 添加天空盒
  addSkybox();

  // 加载地图模型
  loadMapModel();

  // 初始化时钟
  clock.value = new THREE.Clock();

  // 处理窗口大小变化
  window.addEventListener('resize', handleResize);

  // 添加ResizeObserver监听容器大小变化
  if (threeContainer.value && window.ResizeObserver) {
    resizeObserver.value = new ResizeObserver(entries => {
      for (let entry of entries) {
        // 当容器大小变化时，调用handleResize
        handleResize();
      }
    });
    resizeObserver.value.observe(threeContainer.value);
  }

  // 添加鼠标事件监听
  if (renderer.value) {
    renderer.value.domElement.addEventListener('contextmenu', handleContextMenu);
    renderer.value.domElement.addEventListener('click', handleCanvasClick);
    renderer.value.domElement.addEventListener('mousedown', handleMouseDown);
    renderer.value.domElement.addEventListener('mouseup', handleMouseUp);
    renderer.value.domElement.addEventListener('mousemove', handleCanvasMouseMove);
  }

  // 开始动画循环
  animate();

  // 初始化相机参数优化
  if (props.enableControls) {
    optimizeCameraParameters();
  }

  // 添加全局键盘事件监听（用于ESC键等）
  window.addEventListener('keydown', handleGlobalKeyDown);
  window.addEventListener('keyup', handleGlobalKeyUp);

  // 通知场景已准备好
  emit('sceneReady', {
    scene: scene.value,
    camera: camera.value,
    renderer: renderer.value,
  });
};

// 添加灯光
const addLights = () => {
  if (!props.sceneConfig.lights || !props.sceneConfig.lights.length) {
    // 默认灯光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    scene.value.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(20, 300, 20);
    directionalLight.castShadow = true;

    // 配置默认平行光的阴影
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.left = -300;
    directionalLight.shadow.camera.right = 300;
    directionalLight.shadow.camera.top = 300;
    directionalLight.shadow.camera.bottom = -300;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 1000;
    directionalLight.shadow.bias = -0.0001;
    directionalLight.shadow.normalBias = 0.02;

    scene.value.add(directionalLight);
    return;
  }

  // 添加配置的灯光
  props.sceneConfig.lights.forEach(lightConfig => {
    let light;

    switch (lightConfig.type) {
      case 'ambient':
        light = new THREE.AmbientLight(lightConfig.color || 0x404040, lightConfig.intensity || 1);
        break;

      case 'directional':
        light = new THREE.DirectionalLight(lightConfig.color || 0xffffff, lightConfig.intensity || 1);

        if (lightConfig.position) {
          light.position.set(lightConfig.position.x || 0, lightConfig.position.y || 1, lightConfig.position.z || 0);
        }

        light.castShadow = lightConfig.castShadow !== false;

        // 配置阴影
        if (light.castShadow) {
          const shadowConfig = lightConfig.shadow || {};

          // 设置阴影贴图大小
          light.shadow.mapSize.width = shadowConfig.mapSize?.width || 2048;
          light.shadow.mapSize.height = shadowConfig.mapSize?.height || 2048;

          // 配置阴影相机范围
          light.shadow.camera.left = shadowConfig.camera?.left || -300;
          light.shadow.camera.right = shadowConfig.camera?.right || 300;
          light.shadow.camera.top = shadowConfig.camera?.top || 300;
          light.shadow.camera.bottom = shadowConfig.camera?.bottom || -300;
          light.shadow.camera.near = shadowConfig.camera?.near || 0.5;
          light.shadow.camera.far = shadowConfig.camera?.far || 1000;

          // 设置阴影偏移以减少阴影痤疮
          light.shadow.bias = shadowConfig.bias || -0.0001;
          light.shadow.normalBias = shadowConfig.normalBias || 0.02;
        }
        break;

      case 'point':
        light = new THREE.PointLight(
          lightConfig.color || 0xffffff,
          lightConfig.intensity || 1,
          lightConfig.distance || 0,
          lightConfig.decay || 2,
        );

        if (lightConfig.position) {
          light.position.set(lightConfig.position.x || 0, lightConfig.position.y || 1, lightConfig.position.z || 0);
          light.target.position.set(0, 0, 0);
        }

        light.castShadow = lightConfig.castShadow !== false;
        break;

      case 'spot':
        light = new THREE.SpotLight(
          lightConfig.color || 0xffffff,
          lightConfig.intensity || 1,
          lightConfig.distance || 0,
          lightConfig.angle || Math.PI / 3,
          lightConfig.penumbra || 0,
          lightConfig.decay || 2,
        );

        if (lightConfig.position) {
          light.position.set(lightConfig.position.x || 0, lightConfig.position.y || 1, lightConfig.position.z || 0);
        }

        light.castShadow = lightConfig.castShadow !== false;
        break;
    }

    if (light) {
      scene.value.add(light);
    }
  });
};

// 添加地面
const addGround = () => {
  if (!props.sceneConfig.ground) return;

  const groundConfig = props.sceneConfig.ground;
  const size = groundConfig.size || 10;

  // 创建地面平面
  const groundGeometry = new THREE.PlaneGeometry(size, size);
  const groundMaterial = new THREE.MeshStandardMaterial({
    color: groundConfig.color || 0x999999,
    roughness: groundConfig.roughness || 0.8,
  });

  const ground = new THREE.Mesh(groundGeometry, groundMaterial);
  ground.rotation.x = -Math.PI / 2;
  ground.receiveShadow = groundConfig.receiveShadow !== false;

  if (groundConfig.position) {
    ground.position.set(groundConfig.position.x || 0, groundConfig.position.y || 0, groundConfig.position.z || 0);
  }

  scene.value.add(ground);

  // 添加网格线帮助用户导航
  // const gridHelper = new THREE.GridHelper(size, size / 10, 0x444444, 0x666666);
  // gridHelper.position.y = 0.01; // 稍微抬高避免z-fighting
  // scene.value.add(gridHelper);

  // 添加坐标轴辅助线（可选）
  // if (size >= 100) {
  //   const axesHelper = new THREE.AxesHelper(size / 4);
  //   axesHelper.position.y = 0.02;
  //   scene.value.add(axesHelper);
  // }
};

// 添加天空盒
const addSkybox = () => {
  console.log('开始添加天空盒');
  // 检查是否配置了天空盒
  if (props.sceneConfig.skybox && props.sceneConfig.skybox.enabled !== false) {
    const skyboxConfig = props.sceneConfig.skybox;

    if (skyboxConfig.type === 'cube' && skyboxConfig.textures) {
      // 立方体天空盒
      addCubeSkybox(skyboxConfig);
    } else if (skyboxConfig.type === 'sphere' && skyboxConfig.texture) {
      // 球形天空盒
      addSphereSkybox(skyboxConfig);
    } else {
      // 默认渐变天空盒
      addGradientSkybox();
    }
  } else {
    // 默认渐变天空盒
    addGradientSkybox();
  }
};

// 添加立方体天空盒
const addCubeSkybox = config => {
  const loader = new THREE.CubeTextureLoader();

  // 设置贴图路径（按照Three.js的标准顺序：+X, -X, +Y, -Y, +Z, -Z）
  const urls = [
    config.textures.px || '/textures/skybox/px.jpg', // 右
    config.textures.nx || '/textures/skybox/nx.jpg', // 左
    config.textures.py || '/textures/skybox/py.jpg', // 上
    config.textures.ny || '/textures/skybox/ny.jpg', // 下
    config.textures.pz || '/textures/skybox/pz.jpg', // 前
    config.textures.nz || '/textures/skybox/nz.jpg', // 后
  ];

  loader.load(
    urls,
    texture => {
      scene.value.background = texture;
      console.log('立方体天空盒加载成功');
    },
    progress => {
      console.log('天空盒加载进度:', (progress.loaded / progress.total) * 100 + '%');
    },
    error => {
      console.warn('立方体天空盒加载失败，使用默认渐变天空盒:', error);
      addGradientSkybox();
    },
  );
};

// 添加球形天空盒
const addSphereSkybox = config => {
  const loader = new THREE.TextureLoader();

  loader.load(
    config.texture,
    texture => {
      // 创建球形几何体
      const geometry = new THREE.SphereGeometry(10000, 32, 32);

      // 创建材质
      const material = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.BackSide, // 内表面渲染
      });

      // 创建天空盒网格
      const skybox = new THREE.Mesh(geometry, material);

      // 设置天空盒属性以防止被裁剪
      skybox.frustumCulled = false; // 禁用视锥体裁剪
      skybox.renderOrder = -1; // 确保天空盒最先渲染
      skybox.userData.type = 'skybox'; // 标记为天空盒

      scene.value.add(skybox);

      console.log('球形天空盒加载成功');
    },
    progress => {
      console.log('天空盒加载进度:', (progress.loaded / progress.total) * 100 + '%');
    },
    error => {
      console.warn('球形天空盒加载失败，使用默认渐变天空盒:', error);
      addGradientSkybox();
    },
  );
};

// 添加默认渐变天空盒
const addGradientSkybox = () => {
  // 创建渐变纹理
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 256;

  const context = canvas.getContext('2d');
  const gradient = context.createLinearGradient(0, 0, 0, 256);

  // 天空渐变色：从天蓝色到地平线的浅蓝色
  gradient.addColorStop(0, '#87CEEB'); // 天蓝色
  gradient.addColorStop(0.5, '#98D8E8'); // 中间色
  gradient.addColorStop(1, '#B0E0E6'); // 浅蓝色

  context.fillStyle = gradient;
  context.fillRect(0, 0, 1, 256);

  // 创建纹理
  const texture = new THREE.CanvasTexture(canvas);

  // 创建球形几何体（与球形天空盒保持一致的半径）
  const geometry = new THREE.SphereGeometry(10000, 32, 32);

  // 创建材质
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.BackSide,
    fog: false,
  });

  // 创建天空盒网格
  const skybox = new THREE.Mesh(geometry, material);

  // 设置天空盒属性以防止被裁剪
  skybox.frustumCulled = false; // 禁用视锥体裁剪
  skybox.renderOrder = -1; // 确保天空盒最先渲染
  skybox.userData.type = 'skybox'; // 标记为天空盒

  scene.value.add(skybox);

  console.log('默认渐变天空盒已创建');
};

// 创建下雨粒子系统
const createRainParticles = () => {
  const particleCount = 2000;
  const particles = new THREE.BufferGeometry();
  const positions = new Float32Array(particleCount * 3);
  const velocities = new Float32Array(particleCount * 3);

  // 初始化粒子位置和速度
  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3;

    // 随机分布在场景上方
    positions[i3] = (Math.random() - 0.5) * 2000; // x
    positions[i3 + 1] = Math.random() * 500 + 200; // y (高度)
    positions[i3 + 2] = (Math.random() - 0.5) * 2000; // z

    // 下落速度
    velocities[i3] = (Math.random() - 0.5) * 2; // x方向轻微偏移
    velocities[i3 + 1] = -Math.random() * 10 - 5; // y方向下落
    velocities[i3 + 2] = (Math.random() - 0.5) * 2; // z方向轻微偏移
  }

  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  particles.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

  // 创建雨滴材质
  const rainMaterial = new THREE.PointsMaterial({
    color: 0x87CEEB,
    size: 2,
    transparent: true,
    opacity: 0.6,
    blending: THREE.AdditiveBlending
  });

  const rainSystem = new THREE.Points(particles, rainMaterial);
  rainSystem.userData = { type: 'rain', velocities };

  return rainSystem;
};

// 创建下雪粒子系统
const createSnowParticles = () => {
  const particleCount = 1500;
  const particles = new THREE.BufferGeometry();
  const positions = new Float32Array(particleCount * 3);
  const velocities = new Float32Array(particleCount * 3);

  // 初始化粒子位置和速度
  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3;

    // 随机分布在场景上方
    positions[i3] = (Math.random() - 0.5) * 2000; // x
    positions[i3 + 1] = Math.random() * 500 + 200; // y (高度)
    positions[i3 + 2] = (Math.random() - 0.5) * 2000; // z

    // 缓慢下落速度
    velocities[i3] = (Math.random() - 0.5) * 1; // x方向轻微飘动
    velocities[i3 + 1] = -Math.random() * 3 - 1; // y方向缓慢下落
    velocities[i3 + 2] = (Math.random() - 0.5) * 1; // z方向轻微飘动
  }

  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  particles.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

  // 创建雪花材质
  const snowMaterial = new THREE.PointsMaterial({
    color: 0xFFFFFF,
    size: 4,
    transparent: true,
    opacity: 0.8,
    blending: THREE.AdditiveBlending
  });

  const snowSystem = new THREE.Points(particles, snowMaterial);
  snowSystem.userData = { type: 'snow', velocities };

  return snowSystem;
};

// 更新粒子系统
const updateParticles = (delta) => {
  if (!weatherParticles.value) return;

  const positions = weatherParticles.value.geometry.attributes.position.array;
  const velocities = weatherParticles.value.userData.velocities;
  const particleCount = positions.length / 3;

  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3;

    // 更新位置
    positions[i3] += velocities[i3] * delta * 60; // x
    positions[i3 + 1] += velocities[i3 + 1] * delta * 60; // y
    positions[i3 + 2] += velocities[i3 + 2] * delta * 60; // z

    // 重置超出边界的粒子
    if (positions[i3 + 1] < -50) { // 粒子落到地面以下
      positions[i3] = (Math.random() - 0.5) * 2000;
      positions[i3 + 1] = Math.random() * 100 + 400;
      positions[i3 + 2] = (Math.random() - 0.5) * 2000;
    }

    // 边界检查 - x和z方向
    if (Math.abs(positions[i3]) > 1000) {
      positions[i3] = (Math.random() - 0.5) * 2000;
    }
    if (Math.abs(positions[i3 + 2]) > 1000) {
      positions[i3 + 2] = (Math.random() - 0.5) * 2000;
    }
  }

  weatherParticles.value.geometry.attributes.position.needsUpdate = true;
};

// 更新天气效果
const updateWeather = (weatherType) => {
  console.log('更新天气效果:', weatherType);

  // 移除现有的粒子系统
  if (weatherParticles.value) {
    scene.value.remove(weatherParticles.value);
    weatherParticles.value.geometry.dispose();
    weatherParticles.value.material.dispose();
    weatherParticles.value = null;
  }

  currentWeatherType.value = weatherType;

  // 根据天气类型创建新的粒子系统
  switch (weatherType) {
    case 'rain':
      weatherParticles.value = createRainParticles();
      scene.value.add(weatherParticles.value);
      console.log('下雨粒子效果已启用');
      break;

    case 'snow':
      weatherParticles.value = createSnowParticles();
      scene.value.add(weatherParticles.value);
      console.log('下雪粒子效果已启用');
      break;

    case 'clear':
    default:
      console.log('晴天模式，无粒子效果');
      break;
  }
};

// 加载地图模型
const loadMapModel = () => {
  // 检查是否配置了地图模型
  if (props.sceneConfig.mapModel && props.sceneConfig.mapModel.enabled !== false) {
    const mapConfig = props.sceneConfig.mapModel;

    if (mapConfig.path) {
      console.log('开始加载地图模型:', mapConfig.path);

      const loader = new GLTFLoader();

      loader.load(
        mapConfig.path,
        gltf => {
          console.log('GLB文件解析成功，开始处理地图模型...');
          const mapModel = gltf.scene;

          // 详细分析地图模型结构
          console.log('地图模型信息:');
          console.log('- 场景对象:', mapModel);
          console.log('- 子对象数量:', mapModel.children.length);

          // 遍历所有子对象，统计网格数量
          let meshCount = 0;
          let totalVertices = 0;
          let totalFaces = 0;

          mapModel.traverse(child => {
            if (child.isMesh) {
              meshCount++;
              child.frustumCulled = false;
              // 启用模型阴影
              child.castShadow = true;
              child.receiveShadow = true;
              child.material.metalness = 0.6
              child.material.roughness = 0.9
              //child.material.emissive = child.material.color; // 设置材质的自发光颜色
              // child.material.emissive.set(0x6a6a6a);
              // child.material.color.set(0x02536c);
              // child.material.emissivemap = child.material.map; // 禁用自发光贴图
              if (child.geometry) {
                const vertices = child.geometry.attributes.position ? child.geometry.attributes.position.count : 0;
                const faces = child.geometry.index ? child.geometry.index.count / 3 : vertices / 3;
                totalVertices += vertices;
                totalFaces += faces;
                console.log(
                  `- 网格 ${meshCount}: ${child.name || 'unnamed'}, 顶点: ${vertices}, 面: ${Math.floor(faces)}`,
                );
              }
            }
          });

          console.log(`地图模型统计: ${meshCount}个网格, ${totalVertices}个顶点, ${Math.floor(totalFaces)}个面`);

          // 计算边界框
          const box = new THREE.Box3().setFromObject(mapModel);
          const size = box.getSize(new THREE.Vector3());
          const center = box.getCenter(new THREE.Vector3());
          console.log('地图边界框:');
          console.log('- 尺寸:', size);
          console.log('- 中心:', center);
          console.log('- 最小点:', box.min);
          console.log('- 最大点:', box.max);

          // 设置地图位置（通常放在地面上）
          if (mapConfig.position) {
            mapModel.position.set(mapConfig.position.x || 0, mapConfig.position.y || 0, mapConfig.position.z || 0);
          } else {
            mapModel.position.set(0, 0, 0); // 默认放在原点
          }

          // 设置地图旋转
          if (mapConfig.rotation) {
            mapModel.rotation.set(
              THREE.MathUtils.degToRad(mapConfig.rotation.x || 0),
              THREE.MathUtils.degToRad(mapConfig.rotation.y || 0),
              THREE.MathUtils.degToRad(mapConfig.rotation.z || 0),
            );
          }

          // 设置地图缩放
          if (mapConfig.scale) {
            if (typeof mapConfig.scale === 'number') {
              mapModel.scale.set(mapConfig.scale, mapConfig.scale, mapConfig.scale);
            } else {
              mapModel.scale.set(mapConfig.scale.x || 1, mapConfig.scale.y || 1, mapConfig.scale.z || 1);
            }
          }

          // 设置阴影和材质检查
          if (mapConfig.receiveShadow !== false) {
            mapModel.traverse(child => {
              if (child.isMesh) {
                child.receiveShadow = true;
                child.castShadow = mapConfig.castShadow !== false; // 默认启用投射阴影

                // 检查材质状态
                if (child.material) {
                  console.log(`材质检查 - ${child.name || 'unnamed'}:`, {
                    type: child.material.type,
                    visible: child.material.visible,
                    transparent: child.material.transparent,
                    opacity: child.material.opacity,
                  });
                }
              }
            });
          }

          // 添加到场景
          scene.value.add(mapModel);

          // 保存地图模型引用
          loadedModels.value['mapModel'] = mapModel;

          console.log('地图模型已添加到场景');
          console.log('当前场景子对象数量:', scene.value.children.length);

          // 触发地图加载完成事件
          emit('mapLoaded', {
            id: 'mapModel',
            model: mapModel,
            config: mapConfig,
            stats: {
              meshCount,
              totalVertices,
              totalFaces,
              boundingBox: { size, center, min: box.min, max: box.max },
            },
          });
        },
        progress => {
          const percent = ((progress.loaded / progress.total) * 100).toFixed(1);
          const loadedMB = (progress.loaded / 1024 / 1024).toFixed(2);
          const totalMB = (progress.total / 1024 / 1024).toFixed(2);

          console.log(`地图加载进度: ${percent}% (${loadedMB}MB / ${totalMB}MB)`);

          // 检查加载是否异常缓慢
          if (progress.loaded > 0 && progress.total > 0) {
            const ratio = progress.loaded / progress.total;
            if (ratio > 0.1 && ratio < 0.9) {
              console.log('地图加载中，请耐心等待...');
            }
          }

          // 触发加载进度事件
          emit('mapLoadProgress', {
            loaded: progress.loaded,
            total: progress.total,
            percent: parseFloat(percent),
            loadedMB: parseFloat(loadedMB),
            totalMB: parseFloat(totalMB),
          });
        },
        error => {
          console.error('地图模型加载失败，详细错误信息:');
          console.error('- 错误对象:', error);
          console.error('- 错误消息:', error.message);
          console.error('- 文件路径:', mapConfig.path);
          console.error('- 错误类型:', error.constructor.name);

          // 检查常见错误原因
          if (error.message.includes('404')) {
            console.error('可能原因: 地图文件不存在或路径错误');
          } else if (error.message.includes('CORS')) {
            console.error('可能原因: 跨域访问被阻止');
          } else if (error.message.includes('network')) {
            console.error('可能原因: 网络连接问题');
          } else {
            console.error('可能原因: 文件格式错误或文件损坏');
          }

          // 触发地图加载错误事件
          emit('mapLoadError', {
            error: error,
            path: mapConfig.path,
            message: error.message,
            type: error.constructor.name,
          });
        },
      );
    } else {
      console.warn('地图模型配置中缺少路径信息');
    }
  } else {
    console.log('未配置地图模型或已禁用');
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (!threeContainer.value || !camera.value || !renderer.value) return;

  const container = threeContainer.value;
  const width = container.clientWidth;
  const height = container.clientHeight;

  camera.value.aspect = width / height;
  camera.value.updateProjectionMatrix();
  renderer.value.setSize(width, height);

  console.log('Scene resized to:', width, 'x', height);
};

// 强制重新调整大小（用于容器大小变化时）
const forceResize = () => {
  // 使用nextTick确保DOM更新完成后再调整大小
  nextTick(() => {
    handleResize();
  });
};

// 动态调整相机参数以防止闪烁
const optimizeCameraParameters = () => {
  if (!camera.value || !controls.value) return;

  // 获取当前相机到目标的距离
  const distance = camera.value.position.distanceTo(controls.value.target);

  // 根据距离动态调整近裁剪面，但确保远裁剪面足够大以包含天空盒
  let nearPlane = 0.01;
  let farPlane = 15000; // 确保远裁剪面大于天空盒半径（10000）

  if (distance < 1) {
    // 极近距离：优化精度，但保持远裁剪面足够大
    nearPlane = 0.001;
    farPlane = 15000; // 保持足够大以包含天空盒
  } else if (distance < 10) {
    // 近距离：平衡精度和范围
    nearPlane = 0.01;
    farPlane = 15000; // 保持足够大以包含天空盒
  } else if (distance < 100) {
    // 中距离：标准设置
    nearPlane = 0.1;
    farPlane = 15000; // 保持足够大以包含天空盒
  } else {
    // 远距离：扩大范围
    nearPlane = 1;
    farPlane = 15000; // 保持足够大以包含天空盒
  }

  // 应用新的裁剪面设置
  if (camera.value.near !== nearPlane || camera.value.far !== farPlane) {
    camera.value.near = nearPlane;
    camera.value.far = farPlane;
    camera.value.updateProjectionMatrix();

    console.log(`相机参数已优化: 距离=${distance.toFixed(2)}, near=${nearPlane}, far=${farPlane}`);
  }
};

// 新的鼠标移动处理 - 用于右键拖拽相机控制
const handleCanvasMouseMove = event => {
  // 如果第一人称模式激活，不处理
  if (firstPersonMode.value) return;

  // 如果右键按下，检测是否开始拖拽
  if (rightMouseDragState.value.isRightMouseDown) {
    const deltaX = Math.abs(event.clientX - rightMouseDragState.value.lastMouseX);
    const deltaY = Math.abs(event.clientY - rightMouseDragState.value.lastMouseY);

    // 如果鼠标移动超过阈值，开始拖拽
    if (!rightMouseDragState.value.isDragging && (deltaX > 3 || deltaY > 3)) {
      rightMouseDragState.value.isDragging = true;
      console.log('开始拖拽相机');
    }

    // 如果正在拖拽，进行相机移动
    if (rightMouseDragState.value.isDragging) {
      const moveX = event.clientX - rightMouseDragState.value.lastMouseX;
      const moveY = event.clientY - rightMouseDragState.value.lastMouseY;

      // 更新相机位置 - 根据鼠标移动方向移动相机
      if (camera.value && controls.value) {
        // 获取相机的右方向和上方向向量
        const cameraDirection = new THREE.Vector3();
        camera.value.getWorldDirection(cameraDirection);

        const rightVector = new THREE.Vector3();
        rightVector.crossVectors(cameraDirection, camera.value.up).normalize();

        const upVector = new THREE.Vector3();
        upVector.crossVectors(rightVector, cameraDirection).normalize();

        // 计算移动量
        const moveSpeed = cameraDragMovement.value.dragSensitivity;
        const moveDeltaX = -moveX * moveSpeed;
        const moveDeltaY = moveY * moveSpeed;

        // 应用移动
        const moveVector = new THREE.Vector3();
        moveVector.addScaledVector(rightVector, moveDeltaX);
        moveVector.addScaledVector(upVector, moveDeltaY);

        camera.value.position.add(moveVector);
        controls.value.target.add(moveVector);
        controls.value.update();
      }

      // 更新鼠标位置
      rightMouseDragState.value.lastMouseX = event.clientX;
      rightMouseDragState.value.lastMouseY = event.clientY;
    }
  }
};

// 鼠标按下事件处理
const handleMouseDown = event => {
  // 如果是右键按下
  if (event.button === 2) {
    rightMouseDragState.value.isRightMouseDown = true;
    rightMouseDragState.value.lastMouseX = event.clientX;
    rightMouseDragState.value.lastMouseY = event.clientY;
    rightMouseDragState.value.isDragging = false;

    // 禁用轨道控制器以避免冲突
    if (controls.value) {
      controls.value.enabled = false;
    }

    console.log('右键按下，准备拖拽相机');
    event.preventDefault();
  }
};

// 鼠标抬起事件处理
const handleMouseUp = event => {
  // 如果是右键抬起
  if (event.button === 2) {
    const wasDragging = rightMouseDragState.value.isDragging;

    rightMouseDragState.value.isRightMouseDown = false;

    // 重新启用轨道控制器
    if (controls.value) {
      controls.value.enabled = true;
    }

    console.log('右键抬起，结束拖拽相机，wasDragging:', wasDragging);

    // 如果刚刚进行了拖拽，延迟重置拖拽状态，防止立即触发右键菜单
    if (wasDragging) {
      setTimeout(() => {
        rightMouseDragState.value.isDragging = false;
        console.log('拖拽状态已重置');
      }, 100); // 100ms延迟
    } else {
      rightMouseDragState.value.isDragging = false;
    }

    event.preventDefault();
  }
};

// 处理右键菜单（阻止默认行为，并区分拖拽和点击）
const handleContextMenu = event => {
  // 总是阻止默认的右键菜单
  event.preventDefault();

  console.log('Context menu triggered, isDragging:', rightMouseDragState.value.isDragging);

  // 如果刚刚进行了拖拽操作，不处理右键菜单
  if (rightMouseDragState.value.isDragging) {
    console.log('拖拽操作刚结束，忽略右键菜单');
    return;
  }

  // 如果第一人称模式激活，不处理点击
  if (firstPersonMode.value) return;

  console.log('处理右键点击事件');

  // 计算鼠标位置
  const rect = renderer.value.domElement.getBoundingClientRect();
  mouse.value.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.value.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线
  raycaster.value.setFromCamera(mouse.value, camera.value);

  // 获取所有可点击的模型
  const clickableObjects = [];
  Object.values(loadedModels.value).forEach(model => {
    if (model) {
      model.traverse(child => {
        if (child.isMesh) {
          clickableObjects.push(child);
        }
      });
    }
  });

  // 检测交集
  const intersects = raycaster.value.intersectObjects(clickableObjects);

  if (intersects.length > 0) {
    // 找到被点击的模型
    const clickedObject = intersects[0].object;
    let clickedModelId = null;

    // 查找模型ID
    Object.entries(loadedModels.value).forEach(([modelId, model]) => {
      if (model) {
        model.traverse(child => {
          if (child === clickedObject) {
            clickedModelId = modelId;
          }
        });
      }
    });

    if (clickedModelId) {
      console.log('Model right-clicked:', clickedModelId);
      // 触发模型右键点击事件，传递鼠标位置用于显示菜单
      emit('model-right-clicked', {
        modelId: clickedModelId,
        mouseX: event.clientX,
        mouseY: event.clientY,
      });
    }
  } else {
    // 没有点击到模型，触发场景右键点击事件
    console.log('Scene right-clicked');
    emit('scene-right-clicked', {
      mouseX: event.clientX,
      mouseY: event.clientY,
      worldPosition: {
        x: mouse.value.x,
        y: mouse.value.y
      }
    });
  }
};

// 处理3D画面点击事件（简化版，不再需要双击检测）
const handleCanvasClick = event => {
  console.log('Canvas clicked');
  // 简单的点击处理，不再需要双击进入自由相机模式
  // 自由相机模式现在通过右键拖拽实现
};

// 简化的键盘事件处理（移除WASD控制，因为现在使用右键拖拽）
const handleGlobalKeyDown = event => {
  // 保留其他键盘功能，但移除自由相机的WASD控制
  // 现在自由相机通过右键拖拽实现
};

const handleGlobalKeyUp = event => {
  // 保留其他键盘功能，但移除自由相机的WASD控制
  // 现在自由相机通过右键拖拽实现
};

// 自由视角相机移动现在通过右键拖拽实现，不再需要键盘控制

// 动画循环
const animate = () => {
  animationId.value = requestAnimationFrame(animate);

  const delta = clock.value.getDelta();

  try {
    // 更新所有模型的动画
    Object.values(modelRefs.value).forEach(modelRef => {
      if (modelRef && modelRef.updateAnimation) {
        modelRef.updateAnimation(delta);
      }
    });

    // 自由视角相机移动现在通过右键拖拽实现，不需要在动画循环中更新

    // 更新天气粒子系统
    updateParticles(delta);

    // 更新控制器
    if (controls.value && !freeCameraMode.value && !firstPersonMode.value) {
      controls.value.update();
    }

    // 渲染场景
    if (renderer.value && scene.value && camera.value) {
      renderer.value.render(toRaw(scene.value), camera.value);
    }
  } catch (error) {
    console.error('渲染错误:', error);
  }
};

// 处理模型加载完成事件
const handleModelLoaded = data => {
  loadedModels.value[data.id] = data.model;
  emit('modelLoaded', data);
};

// 处理模型加载错误事件
const handleModelError = data => {
  emit('modelError', data);
};

// 获取模型引用
const setModelRef = (id, el) => {
  if (el) {
    modelRefs.value[id] = el;
  }
};

// 更新模型位置（即时移动）
const updateModelPosition = (modelId, position, rotation = null) => {
  const modelRef = modelRefs.value[modelId];
  if (!modelRef) {
    console.warn(`⚠️ 未找到模型引用: ${modelId}`);
    return;
  }

  // 使用ModelComponent的方法更新位置
  if (position && modelRef.updateCurrentPosition) {
    modelRef.updateCurrentPosition(position);
    console.log(`✅ 更新模型 ${modelId} 位置:`, position);
  }

  // 使用ModelComponent的方法更新角度
  if (rotation && modelRef.updateCurrentRotation) {
    modelRef.updateCurrentRotation(rotation);
    console.log(`✅ 更新模型 ${modelId} 角度:`, rotation);
  }
};

// 平滑移动模型到目标位置（地面坐标系，使用匀速线性动画）
const moveModelToPosition = async (modelId, position, speed = null, immediate = false) => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.moveToPosition) {
    return await modelRef.moveToPosition(position, speed, 'linear', immediate); // 使用匀速线性动画
  }
};

// 立即设置模型位置（地面坐标系）
const setModelPositionImmediate = (modelId, position) => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.setPositionImmediate) {
    modelRef.setPositionImmediate(position);
  }
};

// 获取模型当前位置（地面坐标系）
const getModelPosition = modelId => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.getCurrentPosition) {
    return modelRef.getCurrentPosition();
  }
  return null;
};

// 获取模型当前世界坐标位置
const getModelWorldPosition = modelId => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.getCurrentWorldPosition) {
    return modelRef.getCurrentWorldPosition();
  }
  return null;
};

// 设置模型移动速度
const setModelMovementSpeed = (modelId, speed) => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.setMovementSpeed) {
    modelRef.setMovementSpeed(speed);
  }
};

// 停止模型移动
const stopModelMovement = modelId => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.stopMovement) {
    modelRef.stopMovement();
  }
};

// 获取模型边界框信息
const getModelBoundingBox = modelId => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.getBoundingBox) {
    return modelRef.getBoundingBox();
  }
  return null;
};

// 更新模型旋转
const updateModelRotation = (modelId, rotation) => {
  const model = loadedModels.value[modelId];
  if (!model) return;

  // 创建一个新的Euler对象而不是直接修改
  const newRotation = new THREE.Euler(
    rotation.x !== undefined ? THREE.MathUtils.degToRad(rotation.x) : model.rotation.x,
    rotation.y !== undefined ? THREE.MathUtils.degToRad(rotation.y) : model.rotation.y,
    rotation.z !== undefined ? THREE.MathUtils.degToRad(rotation.z) : model.rotation.z,
  );

  model.rotation.copy(newRotation);
};

// 更新模型缩放
const updateModelScale = (modelId, scale) => {
  const model = loadedModels.value[modelId];
  if (!model) return;

  // 创建一个新的Vector3对象而不是直接修改
  let newScale;
  if (typeof scale === 'number') {
    newScale = new THREE.Vector3(scale, scale, scale);
  } else {
    newScale = new THREE.Vector3(
      scale.x !== undefined ? scale.x : model.scale.x,
      scale.y !== undefined ? scale.y : model.scale.y,
      scale.z !== undefined ? scale.z : model.scale.z,
    );
  }

  model.scale.copy(newScale);
};

// 仿真模型动画
const playModelAnimation = (modelId, animationIndex) => {
  const modelRef = modelRefs.value[modelId];
  if (modelRef && modelRef.playAnimation) {
    modelRef.playAnimation(animationIndex);
  }
};

// 简化的自由视角模式切换（现在主要用于状态管理，实际控制通过右键拖拽实现）
const toggleFreeCameraMode = enabled => {
  freeCameraMode.value = enabled;
  console.log('自由视角模式状态:', enabled ? '启用' : '禁用');
  console.log('现在可以通过长按右键拖拽来移动相机位置');
};

// 设置相机移动速度
const setCameraMovementSpeed = speed => {
  cameraMovementSpeed.value = speed;
};

// 获取自由视角状态
const getFreeCameraMode = () => {
  return freeCameraMode.value;
};

// 获取相机移动速度
const getCameraMovementSpeed = () => {
  return cameraMovementSpeed.value;
};

// 设置第一人称模式
const setFirstPersonMode = enabled => {
  firstPersonMode.value = enabled;

  if (enabled) {
    // 进入第一人称模式时，禁用轨道控制器
    if (controls.value) {
      controls.value.enabled = false;
    }

    // 确保自由视角模式被禁用
    if (freeCameraMode.value) {
      toggleFreeCameraMode(false);
    }

    console.log('第一人称模式已启用：相机控制被锁定');
  } else {
    // 退出第一人称模式时，恢复轨道控制器
    if (controls.value) {
      controls.value.enabled = true;
    }

    console.log('第一人称模式已禁用：相机控制已恢复');
  }
};

// 获取第一人称模式状态
const getFirstPersonMode = () => {
  return firstPersonMode.value;
};

// 重复的函数已删除，使用 handleContextMenu 代替

// 在组件挂载后初始化Three.js
onMounted(() => {
  initThree();
});

// 在组件卸载前清理资源
onBeforeUnmount(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value);
  }

  // 清理天气粒子系统
  if (weatherParticles.value) {
    scene.value.remove(weatherParticles.value);
    weatherParticles.value.geometry.dispose();
    weatherParticles.value.material.dispose();
    weatherParticles.value = null;
  }

  if (renderer.value) {
    renderer.value.dispose();
  }

  if (threeContainer.value && renderer.value) {
    threeContainer.value.removeChild(renderer.value.domElement);
  }

  // 清理全局键盘事件监听
  window.removeEventListener('keydown', handleGlobalKeyDown);
  window.removeEventListener('keyup', handleGlobalKeyUp);

  if (renderer.value) {
    renderer.value.domElement.removeEventListener('click', handleCanvasClick);
    renderer.value.domElement.removeEventListener('contextmenu', handleContextMenu);
    renderer.value.domElement.removeEventListener('mousedown', handleMouseDown);
    renderer.value.domElement.removeEventListener('mouseup', handleMouseUp);
    renderer.value.domElement.removeEventListener('mousemove', handleCanvasMouseMove);
  }

  // 退出指针锁定
  if (document.pointerLockElement) {
    document.exitPointerLock();
  }

  window.removeEventListener('resize', handleResize);

  // 清理ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }
});

// 更新地图材质
const updateMapMaterial = (materialSettings) => {
  if (!scene.value) {
    console.warn('场景未初始化，无法更新地图材质');
    return;
  }

  console.log('🎨 更新地图材质:', materialSettings);

  // 查找场景中的地图对象
  const mapObjects = [];
  scene.value.traverse((child) => {
    if (child.userData && child.userData.type === 'map') {
      mapObjects.push(child);
    }
  });

  console.log(`📦 找到 ${mapObjects.length} 个地图对象`);

  // 更新每个地图对象的材质
  mapObjects.forEach((mapObject, index) => {
    console.log(`🎨 更新地图对象 ${index + 1} 的材质`);

    mapObject.traverse((child) => {
      if (child.isMesh && child.material) {
        // 确保材质支持metalness和roughness
        if (child.material.isMeshStandardMaterial || child.material.isMeshPhysicalMaterial) {
          if (materialSettings.metalness !== undefined) {
            child.material.metalness = materialSettings.metalness;
          }
          if (materialSettings.roughness !== undefined) {
            child.material.roughness = materialSettings.roughness;
          }

          // 标记材质需要更新
          child.material.needsUpdate = true;

          console.log(`✅ 更新材质: ${child.name || 'unnamed'} - metalness: ${child.material.metalness}, roughness: ${child.material.roughness}`);
        } else {
          console.warn(`⚠️ 材质类型不支持PBR属性: ${child.material.type}`);
        }
      }
    });
  });

  console.log(`地图材质更新完成，影响 ${mapObjects.length} 个对象`);
};

// 更新环境光
const updateAmbientLight = (color, intensity) => {
  if (!scene.value) {
    console.warn('场景未初始化，无法更新环境光');
    return;
  }

  console.log('更新环境光:', { color: `0x${color.toString(16)}`, intensity });

  // 查找场景中的环境光
  let ambientLight = null;
  scene.value.traverse((child) => {
    if (child.isAmbientLight) {
      ambientLight = child;
    }
  });

  if (ambientLight) {
    // 更新现有环境光
    ambientLight.color.setHex(color);
    ambientLight.intensity = intensity;
    console.log(`环境光已更新: 颜色=0x${color.toString(16)}, 强度=${intensity}`);
  } else {
    // 创建新的环境光
    ambientLight = new THREE.AmbientLight(color, intensity);
    scene.value.add(ambientLight);
    console.log(`新环境光已创建: 颜色=0x${color.toString(16)}, 强度=${intensity}`);
  }
};

// 测试天空盒可见性的调试函数
const testSkyboxVisibility = () => {
  if (!scene.value || !camera.value) {
    console.warn('场景或相机未初始化');
    return;
  }

  console.log('🔍 天空盒可见性测试:');
  console.log(`📷 相机位置: x=${camera.value.position.x.toFixed(2)}, y=${camera.value.position.y.toFixed(2)}, z=${camera.value.position.z.toFixed(2)}`);
  console.log(`📷 相机裁剪面: near=${camera.value.near}, far=${camera.value.far}`);

  // 查找天空盒对象
  let skyboxCount = 0;
  scene.value.traverse((child) => {
    if (child.userData && child.userData.type === 'skybox') {
      skyboxCount++;
      console.log(`🌌 天空盒 ${skyboxCount}:`, {
        visible: child.visible,
        frustumCulled: child.frustumCulled,
        renderOrder: child.renderOrder,
        geometry: child.geometry ? `半径=${child.geometry.parameters?.radius || 'N/A'}` : 'N/A'
      });
    }
  });

  if (skyboxCount === 0) {
    console.log('⚠️ 未找到天空盒对象');
  } else {
    console.log(`✅ 找到 ${skyboxCount} 个天空盒对象`);
  }

  // 检查场景背景
  if (scene.value.background) {
    console.log('🎨 场景背景类型:', scene.value.background.constructor.name);
  } else {
    console.log('⚠️ 场景无背景设置');
  }
};

// 暴露方法给父组件
defineExpose({
  updateModelPosition,
  updateModelRotation,
  updateModelScale,
  playModelAnimation,
  moveModelToPosition,
  setModelPositionImmediate,
  getModelPosition,
  getModelWorldPosition,
  setModelMovementSpeed,
  stopModelMovement,
  getModelBoundingBox,
  toggleFreeCameraMode,
  setCameraMovementSpeed,
  getFreeCameraMode,
  getCameraMovementSpeed,
  setFirstPersonMode,
  getFirstPersonMode,
  getScene: () => scene.value,
  getCamera: () => camera.value,
  getRenderer: () => renderer.value,
  updateWeather,
  updateMapMaterial,
  updateAmbientLight,
  getControls: () => controls.value,
  forceResize, // 暴露强制重新调整大小的方法
  testSkyboxVisibility, // 暴露天空盒可见性测试函数
  getModelRef: (modelId) => modelRefs.value[modelId] || null, // 获取模型引用
  // 暴露内部状态供调试和路线控制使用
  loadedModels: loadedModels.value,
  modelRefs: modelRefs.value,
});
</script>

<template>
  <div class="scene-manager" :style="{ height }">
    <div class="three-container" ref="threeContainer"></div>

    <!-- 动态创建模型组件 -->
    <ModelComponent
      v-for="model in models"
      :key="model.id"
      :model-config="model"
      :scene="scene"
      @loaded="handleModelLoaded"
      @error="handleModelError"
      :ref="el => setModelRef(model.id, el)"
    />

    <!-- 插槽，用于添加UI控件等 -->
    <div class="overlay-content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.scene-manager {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
}

.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.overlay-content > * {
  pointer-events: auto;
}
</style>
